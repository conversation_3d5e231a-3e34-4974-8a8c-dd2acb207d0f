import 'dart:convert';
import 'package:http/http.dart' as http;
import 'user_model.dart';

class ApiService {
  static const String baseUrl = 'http://************/api/'; // Change to your WAMP server IP/port

  Future<User> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('${baseUrl}login.php'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'password': password}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['success']) {
        return User.fromJson(data['user']);
      } else {
        throw Exception(data['message'] ?? 'Échec de la connexion');
      }
    } else {
      throw Exception('Erreur serveur: ${response.statusCode}');
    }
  }

  Future<User> register(String name, String email, String password) async {
    final response = await http.post(
      Uri.parse('${baseUrl}register.php'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'name': name, 'email': email, 'password': password}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['success']) {
        return User.fromJson(data['user']);
      } else {
        throw Exception(data['message'] ?? 'Échec de l\'inscription');
      }
    } else {
      throw Exception('Erreur serveur: ${response.statusCode}');
    }
  }

  Future<User> getProfile(int userId) async {
    final response = await http.post(
      Uri.parse('${baseUrl}get_profile.php'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'user_id': userId}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['success']) {
        return User.fromJson(data['user']);
      } else {
        throw Exception(data['message'] ?? 'Échec du chargement du profil');
      }
    } else {
      throw Exception('Erreur serveur: ${response.statusCode}');
    }
  }
}