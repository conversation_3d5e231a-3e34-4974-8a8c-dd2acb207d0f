import 'package:flutter/material.dart';
import 'HomeScreen.dart';
import 'api_service.dart';
import 'user_model.dart';
// Importez si dans un fichier séparé

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  bool isLogin = true;
  final _formKey = GlobalKey<FormState>();

  final nameController = TextEditingController(); // Ajouté pour le nom
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFF0F5),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.account_circle_rounded, size: 80, color: Colors.pinkAccent),
              const SizedBox(height: 20),
              Text(
                isLogin ? 'Bienvenue 👋' : 'Créer un compte ✨',
                style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.black87),
              ),
              const SizedBox(height: 8),
              Text(
                isLogin
                    ? 'Connectez-vous pour continuer'
                    : 'Veuillez remplir les informations ci-dessous',
                style: const TextStyle(color: Colors.black54),
              ),
              const SizedBox(height: 32),
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Nom (seulement pour inscription)
                    if (!isLogin)
                      TextFormField(
                        controller: nameController,
                        decoration: InputDecoration(
                          hintText: 'Nom',
                          prefixIcon: const Icon(Icons.person),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                        validator: (value) => value!.isEmpty ? 'Nom requis' : null,
                      ),
                    if (!isLogin) const SizedBox(height: 16),

                    // Email
                    TextFormField(
                      controller: emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: InputDecoration(
                        hintText: 'Email',
                        prefixIcon: const Icon(Icons.email),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      validator: (value) {
                        if (value!.isEmpty) return 'Email requis';
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                          return 'Email invalide';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Mot de passe
                    TextFormField(
                      controller: passwordController,
                      obscureText: true,
                      decoration: InputDecoration(
                        hintText: 'Mot de passe',
                        prefixIcon: const Icon(Icons.lock),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      validator: (value) => value!.isEmpty ? 'Mot de passe requis' : null,
                    ),
                    const SizedBox(height: 16),

                    // Confirm password (if signup)
                    if (!isLogin)
                      TextFormField(
                        controller: confirmPasswordController,
                        obscureText: true,
                        decoration: InputDecoration(
                          hintText: 'Confirmer le mot de passe',
                          prefixIcon: const Icon(Icons.lock_outline),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                        validator: (value) {
                          if (value != passwordController.text) {
                            return 'Les mots de passe ne correspondent pas';
                          }
                          return null;
                        },
                      ),

                    const SizedBox(height: 24),

                    // Bouton d’action
                    SizedBox(
                      width: double.infinity,
                      child: _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : ElevatedButton(
                              onPressed: () async {
                                if (_formKey.currentState!.validate()) {
                                  setState(() => _isLoading = true);
                                  try {
                                    User user;
                                    if (isLogin) {
                                      user = await ApiService().login(
                                        emailController.text,
                                        passwordController.text,
                                      );
                                    } else {
                                      user = await ApiService().register(
                                        nameController.text,
                                        emailController.text,
                                        passwordController.text,
                                      );
                                    }
                                    // Naviguer vers le profil
                                    Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                    builder: (context) => HomeScreen(userId: user.id),
                                    ),
                                    );
                                  } catch (e) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(content: Text(e.toString())),
                                    );
                                  } finally {
                                    setState(() => _isLoading = false);
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.pinkAccent,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12)),
                              ),
                              child: Text(
                                isLogin ? 'Se connecter' : 'S’inscrire',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                    ),
                    const SizedBox(height: 16),

                    // Lien changer de mode
                    TextButton(
                      onPressed: () => setState(() => isLogin = !isLogin),
                      child: Text(
                        isLogin
                            ? "Pas encore de compte ? S’inscrire"
                            : "Déjà inscrit ? Se connecter",
                        style: const TextStyle(color: Colors.pinkAccent),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}